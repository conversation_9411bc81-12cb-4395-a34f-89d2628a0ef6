#include "Bitterroot.h"

ImVec2 DayZ::Bitterroot::translatePoint(const DMARender::Vector3& gamePoint)
{
    // Bitterroot map - 12800 base size with position correction
    float mult = 2048.0f / 12800.0f;

    // Apply small offsets to correct position
    // Player was showing right and down, so we offset left and up
    float offsetX = -200.0f;  // Negative to move left (adjust as needed)
    float offsetZ = -200.0f;  // Negative to move up (adjust as needed)

    return ImVec2((gamePoint.x + offsetX) * mult, (12800.0f - (gamePoint.z + offsetZ)) * mult);
}
