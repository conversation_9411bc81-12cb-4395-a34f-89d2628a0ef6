#include "Bitterroot.h"

ImVec2 DayZ::Bitterroot::translatePoint(const DMARender::Vector3& gamePoint)
{
    // Bitterroot map - try different coordinate systems
    // Option 1: Try 12800 base size (like Namalsk/Livonia)
    float mult = 2048.0f / 12800.0f;
    return ImVec2(gamePoint.x * mult, (12800.0f - gamePoint.z) * mult);

    // Option 2: Try without Y-axis inversion (uncomment to test)
    // return ImVec2(gamePoint.x * mult, gamePoint.z * mult);

    // Option 3: Try with coordinate offset (uncomment to test)
    // return ImVec2((gamePoint.x + 6400.0f) * mult, (12800.0f - (gamePoint.z + 6400.0f)) * mult);
}
