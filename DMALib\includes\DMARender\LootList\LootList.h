#pragma once
#include <vector>
#include <string>
#include <memory>
#include <chrono>
#include <fstream>
//#include <json/json.h>  // Disabled for now
#include "../Vectors/Vector3.h"

namespace DMARender {

    struct SavedLootItem {
        std::string itemName;
        std::string itemType;
        std::string cleanName;
        Vector3 position;
        std::chrono::system_clock::time_point timeFound;
        float distanceWhenSaved;
        bool isRare;
        std::string notes;
        uint32_t entityId;

        SavedLootItem() = default;
        
        SavedLootItem(const std::string& name, const std::string& type, const std::string& clean,
                     const Vector3& pos, float dist, bool rare = false, const std::string& note = "")
            : itemName(name), itemType(type), cleanName(clean), position(pos), 
              timeFound(std::chrono::system_clock::now()), distanceWhenSaved(dist), 
              isRare(rare), notes(note), entityId(0) {}

        // Convert to string for saving
        std::string toString() const;

        // Create from string for loading
        static SavedLootItem fromString(const std::string& str);
        
        // Get time as string
        std::string getTimeString() const;
    };

    class LootListManager {
    private:
        std::vector<SavedLootItem> savedItems;
        std::string saveFilePath;
        bool isDirty;
        
    public:
        LootListManager();
        ~LootListManager();

        // Item management
        void addItem(const SavedLootItem& item);
        void removeItem(size_t index);
        void clearAll();
        void updateNotes(size_t index, const std::string& notes);
        
        // Access
        const std::vector<SavedLootItem>& getItems() const { return savedItems; }
        size_t getItemCount() const { return savedItems.size(); }
        bool isEmpty() const { return savedItems.empty(); }
        
        // Search and filter
        std::vector<size_t> findItemsByName(const std::string& name) const;
        std::vector<size_t> findItemsByType(const std::string& type) const;
        std::vector<size_t> findRareItems() const;
        std::vector<size_t> findItemsNearPosition(const Vector3& pos, float maxDistance) const;
        
        // Persistence
        bool saveToFile();
        bool loadFromFile();
        void setSaveFilePath(const std::string& path) { saveFilePath = path; }
        
        // Utility
        void sortByDistance(const Vector3& currentPos);
        void sortByTime();
        void sortByName();
        
        // Statistics
        int getItemCountByType(const std::string& type) const;
        float getAverageDistance() const;
    };

} // namespace DMARender
