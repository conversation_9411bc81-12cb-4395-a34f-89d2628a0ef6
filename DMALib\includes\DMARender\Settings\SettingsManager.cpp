#include "SettingsManager.h"
#include "../RenderWindow/RenderBridge/RenderBridge.h"
#include <sstream>

namespace DMARender {

    SettingsManager::SettingsManager(const std::string& filePath) 
        : settingsFilePath(filePath) {
        loadSettings();
    }

    bool SettingsManager::saveSettings() {
        try {
            std::ofstream file(settingsFilePath);
            if (!file.is_open()) {
                std::cerr << "Failed to open settings file for writing: " << settingsFilePath << std::endl;
                return false;
            }

            file << "[Radar]" << std::endl;
            file << "showDeadPlayers=" << (settings.showDeadPlayers ? "1" : "0") << std::endl;
            file << "showVehicles=" << (settings.showVehicles ? "1" : "0") << std::endl;
            file << "showGrounditems=" << (settings.showGrounditems ? "1" : "0") << std::endl;
            file << "showBoats=" << (settings.showBoats ? "1" : "0") << std::endl;
            file << "showDeadAnimals=" << (settings.showDeadAnimals ? "1" : "0") << std::endl;
            file << "showClothing=" << (settings.showClothing ? "1" : "0") << std::endl;
            file << "showWeapons=" << (settings.showWeapons ? "1" : "0") << std::endl;
            file << "showProxyMagazines=" << (settings.showProxyMagazines ? "1" : "0") << std::endl;
            file << "showBackpacks=" << (settings.showBackpacks ? "1" : "0") << std::endl;
            file << "showRare=" << (settings.showRare ? "1" : "0") << std::endl;
            file << "showFood=" << (settings.showFood ? "1" : "0") << std::endl;
            file << "showAmmo=" << (settings.showAmmo ? "1" : "0") << std::endl;
            file << "showPlayerList=" << (settings.showPlayerList ? "1" : "0") << std::endl;
            file << "showServerPlayerList=" << (settings.showServerPlayerList ? "1" : "0") << std::endl;
            file << "showZombies=" << (settings.showZombies ? "1" : "0") << std::endl;
            file << "showAnimals=" << (settings.showAnimals ? "1" : "0") << std::endl;
            file << "playername=" << settings.playername << std::endl;
            file << "BlipSize=" << settings.BlipSize << std::endl;
            file << "BlipSize2=" << settings.BlipSize2 << std::endl;
            file << "Aimlinelength=" << settings.Aimlinelength << std::endl;
            file << "showOptics=" << (settings.showOptics ? "1" : "0") << std::endl;
            file << "showBase=" << (settings.showBase ? "1" : "0") << std::endl;
            file << "showMelee=" << (settings.showMelee ? "1" : "0") << std::endl;
            file << "LootDistanceDeadzone=" << settings.LootDistanceDeadzone << std::endl;
            file << "showExplosives=" << (settings.showExplosives ? "1" : "0") << std::endl;
            file << "ZombiesBlipSize=" << settings.ZombiesBlipSize << std::endl;
            file << "AnimalsBlipSize=" << settings.AnimalsBlipSize << std::endl;
            file << "FontRADAR=" << settings.FontRADAR << std::endl;
            file << "FontRADAR2=" << settings.FontRADAR2 << std::endl;
            file << "showPlayerNameRadar=" << (settings.showPlayerNameRadar ? "1" : "0") << std::endl;
            file << "showPlayerDistanceRadar=" << (settings.showPlayerDistanceRadar ? "1" : "0") << std::endl;
            file << "showPlayerHandRadar=" << (settings.showPlayerHandRadar ? "1" : "0") << std::endl;

            file << std::endl << "[ESP]" << std::endl;
            file << "showDeadPlayersFUSER=" << (settings.showDeadPlayersFUSER ? "1" : "0") << std::endl;
            file << "showVehiclesFUSER=" << (settings.showVehiclesFUSER ? "1" : "0") << std::endl;
            file << "showGrounditemsFUSER=" << (settings.showGrounditemsFUSER ? "1" : "0") << std::endl;
            file << "showBoatsFUSER=" << (settings.showBoatsFUSER ? "1" : "0") << std::endl;
            file << "showDeadAnimalsFUSER=" << (settings.showDeadAnimalsFUSER ? "1" : "0") << std::endl;
            file << "showClothingFUSER=" << (settings.showClothingFUSER ? "1" : "0") << std::endl;
            file << "showWeaponsFUSER=" << (settings.showWeaponsFUSER ? "1" : "0") << std::endl;
            file << "showProxyMagazinesFUSER=" << (settings.showProxyMagazinesFUSER ? "1" : "0") << std::endl;
            file << "showBackpacksFUSER=" << (settings.showBackpacksFUSER ? "1" : "0") << std::endl;
            file << "showRareFUSER=" << (settings.showRareFUSER ? "1" : "0") << std::endl;
            file << "showFoodFUSER=" << (settings.showFoodFUSER ? "1" : "0") << std::endl;
            file << "showAmmoFUSER=" << (settings.showAmmoFUSER ? "1" : "0") << std::endl;
            file << "showZombiesFUSER=" << (settings.showZombiesFUSER ? "1" : "0") << std::endl;
            file << "showAnimalsFUSER=" << (settings.showAnimalsFUSER ? "1" : "0") << std::endl;
            file << "lootDistanceFUSER=" << settings.lootDistanceFUSER << std::endl;
            file << "ZombieDistanceFUSER=" << settings.ZombieDistanceFUSER << std::endl;
            file << "showPlayerInfoesp=" << (settings.showPlayerInfoesp ? "1" : "0") << std::endl;
            file << "showPlayerNameFUSER=" << (settings.showPlayerNameFUSER ? "1" : "0") << std::endl;
            file << "showPlayerDistanceFUSER=" << (settings.showPlayerDistanceFUSER ? "1" : "0") << std::endl;
            file << "showPlayerHandFUSER=" << (settings.showPlayerHandFUSER ? "1" : "0") << std::endl;
            file << "showPlayerBoxFUSER=" << (settings.showPlayerBoxFUSER ? "1" : "0") << std::endl;
            file << "showOpticsFUSER=" << (settings.showOpticsFUSER ? "1" : "0") << std::endl;
            file << "showDebugFUSER=" << (settings.showDebugFUSER ? "1" : "0") << std::endl;
            file << "showBaseFUSER=" << (settings.showBaseFUSER ? "1" : "0") << std::endl;
            file << "showMeleeFUSER=" << (settings.showMeleeFUSER ? "1" : "0") << std::endl;
            file << "showExplosivesFUSER=" << (settings.showExplosivesFUSER ? "1" : "0") << std::endl;
            file << "LootDebugDistance=" << settings.LootDebugDistance << std::endl;
            file << "showSavedLootItems=" << (settings.showSavedLootItems ? "1" : "0") << std::endl;

            file << std::endl << "[UI]" << std::endl;
            file << "followPlayerEnabled=" << (settings.followPlayerEnabled ? "1" : "0") << std::endl;

            file.close();
            std::cout << "Settings saved to: " << settingsFilePath << std::endl;
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Error saving settings: " << e.what() << std::endl;
            return false;
        }
    }

    bool SettingsManager::loadSettings() {
        try {
            std::ifstream file(settingsFilePath);
            if (!file.is_open()) {
                std::cout << "Settings file not found, using defaults: " << settingsFilePath << std::endl;
                return false; // File doesn't exist, use defaults
            }

            std::string line;
            std::string currentSection;

            while (std::getline(file, line)) {
                // Remove whitespace
                line.erase(0, line.find_first_not_of(" \t"));
                line.erase(line.find_last_not_of(" \t") + 1);

                // Skip empty lines and comments
                if (line.empty() || line[0] == '#' || line[0] == ';') {
                    continue;
                }

                // Check for section headers
                if (line[0] == '[' && line.back() == ']') {
                    currentSection = line.substr(1, line.length() - 2);
                    continue;
                }

                // Parse key=value pairs
                size_t equalPos = line.find('=');
                if (equalPos == std::string::npos) {
                    continue;
                }

                std::string key = line.substr(0, equalPos);
                std::string value = line.substr(equalPos + 1);

                // Parse the settings based on key
                parseSettingValue(key, value);
            }

            file.close();
            std::cout << "Settings loaded from: " << settingsFilePath << std::endl;
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Error loading settings: " << e.what() << std::endl;
            return false;
        }
    }

    void SettingsManager::parseSettingValue(const std::string& key, const std::string& value) {
        // Helper function to convert string to bool
        auto toBool = [](const std::string& str) { return str == "1" || str == "true"; };
        auto toInt = [](const std::string& str) { return std::stoi(str); };

        // Radar settings
        if (key == "showDeadPlayers") settings.showDeadPlayers = toBool(value);
        else if (key == "showVehicles") settings.showVehicles = toBool(value);
        else if (key == "showGrounditems") settings.showGrounditems = toBool(value);
        else if (key == "showBoats") settings.showBoats = toBool(value);
        else if (key == "showDeadAnimals") settings.showDeadAnimals = toBool(value);
        else if (key == "showClothing") settings.showClothing = toBool(value);
        else if (key == "showWeapons") settings.showWeapons = toBool(value);
        else if (key == "showProxyMagazines") settings.showProxyMagazines = toBool(value);
        else if (key == "showBackpacks") settings.showBackpacks = toBool(value);
        else if (key == "showRare") settings.showRare = toBool(value);
        else if (key == "showFood") settings.showFood = toBool(value);
        else if (key == "showAmmo") settings.showAmmo = toBool(value);
        else if (key == "showPlayerList") settings.showPlayerList = toBool(value);
        else if (key == "showServerPlayerList") settings.showServerPlayerList = toBool(value);
        else if (key == "showZombies") settings.showZombies = toBool(value);
        else if (key == "showAnimals") settings.showAnimals = toBool(value);
        else if (key == "playername") settings.playername = value;
        else if (key == "BlipSize") settings.BlipSize = toInt(value);
        else if (key == "BlipSize2") settings.BlipSize2 = toInt(value);
        else if (key == "Aimlinelength") settings.Aimlinelength = toInt(value);
        else if (key == "showOptics") settings.showOptics = toBool(value);
        else if (key == "showBase") settings.showBase = toBool(value);
        else if (key == "showMelee") settings.showMelee = toBool(value);
        else if (key == "LootDistanceDeadzone") settings.LootDistanceDeadzone = toInt(value);
        else if (key == "showExplosives") settings.showExplosives = toBool(value);
        else if (key == "ZombiesBlipSize") settings.ZombiesBlipSize = toInt(value);
        else if (key == "AnimalsBlipSize") settings.AnimalsBlipSize = toInt(value);
        else if (key == "FontRADAR") settings.FontRADAR = toInt(value);
        else if (key == "FontRADAR2") settings.FontRADAR2 = toInt(value);
        else if (key == "showPlayerNameRadar") settings.showPlayerNameRadar = toBool(value);
        else if (key == "showPlayerDistanceRadar") settings.showPlayerDistanceRadar = toBool(value);
        else if (key == "showPlayerHandRadar") settings.showPlayerHandRadar = toBool(value);

        // ESP settings
        else if (key == "showDeadPlayersFUSER") settings.showDeadPlayersFUSER = toBool(value);
        else if (key == "showVehiclesFUSER") settings.showVehiclesFUSER = toBool(value);
        else if (key == "showGrounditemsFUSER") settings.showGrounditemsFUSER = toBool(value);
        else if (key == "showBoatsFUSER") settings.showBoatsFUSER = toBool(value);
        else if (key == "showDeadAnimalsFUSER") settings.showDeadAnimalsFUSER = toBool(value);
        else if (key == "showClothingFUSER") settings.showClothingFUSER = toBool(value);
        else if (key == "showWeaponsFUSER") settings.showWeaponsFUSER = toBool(value);
        else if (key == "showProxyMagazinesFUSER") settings.showProxyMagazinesFUSER = toBool(value);
        else if (key == "showBackpacksFUSER") settings.showBackpacksFUSER = toBool(value);
        else if (key == "showRareFUSER") settings.showRareFUSER = toBool(value);
        else if (key == "showFoodFUSER") settings.showFoodFUSER = toBool(value);
        else if (key == "showAmmoFUSER") settings.showAmmoFUSER = toBool(value);
        else if (key == "showZombiesFUSER") settings.showZombiesFUSER = toBool(value);
        else if (key == "showAnimalsFUSER") settings.showAnimalsFUSER = toBool(value);
        else if (key == "lootDistanceFUSER") settings.lootDistanceFUSER = toInt(value);
        else if (key == "ZombieDistanceFUSER") settings.ZombieDistanceFUSER = toInt(value);
        else if (key == "showPlayerInfoesp") settings.showPlayerInfoesp = toBool(value);
        else if (key == "showPlayerNameFUSER") settings.showPlayerNameFUSER = toBool(value);
        else if (key == "showPlayerDistanceFUSER") settings.showPlayerDistanceFUSER = toBool(value);
        else if (key == "showPlayerHandFUSER") settings.showPlayerHandFUSER = toBool(value);
        else if (key == "showPlayerBoxFUSER") settings.showPlayerBoxFUSER = toBool(value);
        else if (key == "showOpticsFUSER") settings.showOpticsFUSER = toBool(value);
        else if (key == "showDebugFUSER") settings.showDebugFUSER = toBool(value);
        else if (key == "showBaseFUSER") settings.showBaseFUSER = toBool(value);
        else if (key == "showMeleeFUSER") settings.showMeleeFUSER = toBool(value);
        else if (key == "showExplosivesFUSER") settings.showExplosivesFUSER = toBool(value);
        else if (key == "LootDebugDistance") settings.LootDebugDistance = toInt(value);
        else if (key == "showSavedLootItems") settings.showSavedLootItems = toBool(value);

        // UI settings
        else if (key == "followPlayerEnabled") settings.followPlayerEnabled = toBool(value);
    }

    void SettingsManager::applyToRenderBridge(RenderBridge* bridge) {
        if (!bridge) return;

        // Apply radar settings
        bridge->setShowDeadPlayers(settings.showDeadPlayers);
        bridge->setShowVehicles(settings.showVehicles);
        bridge->setShowGrounditems(settings.showGrounditems);
        bridge->setShowBoats(settings.showBoats);
        bridge->setShowDeadAnimals(settings.showDeadAnimals);
        bridge->setShowClothing(settings.showClothing);
        bridge->setShowWeapons(settings.showWeapons);
        bridge->setShowProxyMagazines(settings.showProxyMagazines);
        bridge->setShowBackpacks(settings.showBackpacks);
        bridge->setShowRare(settings.showRare);
        bridge->setShowFood(settings.showFood);
        bridge->setShowAmmo(settings.showAmmo);
        bridge->setShowPlayerList(settings.showPlayerList);
        bridge->setShowServerPlayerList(settings.showServerPlayerList);
        bridge->setShowZombies(settings.showZombies);
        bridge->setShowAnimals(settings.showAnimals);
        bridge->setShowPlayerName(settings.playername);
        bridge->setShowBlipSize(settings.BlipSize);
        bridge->setShowBlipSize2(settings.BlipSize2);
        bridge->setAimlinelength(settings.Aimlinelength);
        bridge->setShowOptics(settings.showOptics);
        bridge->setShowBase(settings.showBase);
        bridge->setShowMelee(settings.showMelee);
        bridge->setLootDistanceDeadzone(settings.LootDistanceDeadzone);
        bridge->setShowExplosives(settings.showExplosives);
        bridge->setZombiesBlipSize(settings.ZombiesBlipSize);
        bridge->setAnimalsBlipSize(settings.AnimalsBlipSize);
        bridge->setRadarFont(settings.FontRADAR);
        bridge->setRadarFont2(settings.FontRADAR2);
        bridge->setShowPlayerNameRadar(settings.showPlayerNameRadar);
        bridge->setShowPlayerDistanceRadar(settings.showPlayerDistanceRadar);
        bridge->setShowPlayerHandRadar(settings.showPlayerHandRadar);

        // Apply ESP settings
        bridge->setShowDeadPlayersFUSER(settings.showDeadPlayersFUSER);
        bridge->setShowVehiclesFUSER(settings.showVehiclesFUSER);
        bridge->setShowGrounditemsFUSER(settings.showGrounditemsFUSER);
        bridge->setShowBoatsFUSER(settings.showBoatsFUSER);
        bridge->setShowDeadAnimalsFUSER(settings.showDeadAnimalsFUSER);
        bridge->setShowClothingFUSER(settings.showClothingFUSER);
        bridge->setShowWeaponsFUSER(settings.showWeaponsFUSER);
        bridge->setShowProxyMagazinesFUSER(settings.showProxyMagazinesFUSER);
        bridge->setShowBackpacksFUSER(settings.showBackpacksFUSER);
        bridge->setShowRareFUSER(settings.showRareFUSER);
        bridge->setShowFoodFUSER(settings.showFoodFUSER);
        bridge->setShowAmmoFUSER(settings.showAmmoFUSER);
        bridge->setShowZombiesFUSER(settings.showZombiesFUSER);
        bridge->setShowAnimalsFUSER(settings.showAnimalsFUSER);
        bridge->setlootDistance(settings.lootDistanceFUSER);
        bridge->setZombieDistance(settings.ZombieDistanceFUSER);
        bridge->setShowPlayerInfoesp(settings.showPlayerInfoesp);
        bridge->setShowPlayerNameFUSER(settings.showPlayerNameFUSER);
        bridge->setShowPlayerDistanceFUSER(settings.showPlayerDistanceFUSER);
        bridge->setShowPlayerHandFUSER(settings.showPlayerHandFUSER);
        bridge->setShowPlayerBoxFUSER(settings.showPlayerBoxFUSER);
        bridge->setShowOpticsFUSER(settings.showOpticsFUSER);
        bridge->setShowDebugFUSER(settings.showDebugFUSER);
        bridge->setShowBaseFUSER(settings.showBaseFUSER);
        bridge->setShowMeleeFUSER(settings.showMeleeFUSER);
        bridge->setShowExplosivesFUSER(settings.showExplosivesFUSER);
        bridge->setLootDebugDistance(settings.LootDebugDistance);
        bridge->setShowSavedLootItems(settings.showSavedLootItems);
    }

    void SettingsManager::loadFromRenderBridge(RenderBridge* bridge) {
        if (!bridge) return;

        // Load radar settings
        settings.showDeadPlayers = bridge->shouldShowDeadPlayers();
        settings.showVehicles = bridge->shouldShowVehicles();
        settings.showGrounditems = bridge->shouldShowGrounditems();
        settings.showBoats = bridge->shouldShowBoats();
        settings.showDeadAnimals = bridge->shouldShowDeadAnimals();
        settings.showClothing = bridge->shouldShowClothing();
        settings.showWeapons = bridge->shouldShowWeapons();
        settings.showProxyMagazines = bridge->shouldShowProxyMagazines();
        settings.showBackpacks = bridge->shouldShowBackpacks();
        settings.showRare = bridge->shouldShowRare();
        settings.showFood = bridge->shouldShowFood();
        settings.showAmmo = bridge->shouldShowAmmo();
        settings.showPlayerList = bridge->shouldShowPlayerList();
        settings.showServerPlayerList = bridge->shouldShowServerPlayerList();
        settings.showZombies = bridge->shouldShowZombies();
        settings.showAnimals = bridge->shouldShowAnimals();
        settings.playername = bridge->shouldPlayerName();
        settings.BlipSize = bridge->shouldBlipSize();
        settings.BlipSize2 = bridge->shouldBlipSize2();
        settings.Aimlinelength = bridge->shouldAimlinelength();
        settings.showOptics = bridge->shouldShowOptics();
        settings.showBase = bridge->shouldShowBase();
        settings.showMelee = bridge->shouldShowMelee();
        settings.LootDistanceDeadzone = bridge->shouldLootDistanceDeadzone();
        settings.showExplosives = bridge->shouldShowExplosives();
        settings.ZombiesBlipSize = bridge->shouldZombiesBlipSize();
        settings.AnimalsBlipSize = bridge->shouldAnimalsBlipSize();
        settings.FontRADAR = bridge->shouldRadarFont();
        settings.FontRADAR2 = bridge->shouldRadarFont2();
        settings.showPlayerNameRadar = bridge->shouldShowPlayerNameRadar();
        settings.showPlayerDistanceRadar = bridge->shouldShowPlayerDistanceRadar();
        settings.showPlayerHandRadar = bridge->shouldShowPlayerHandRadar();

        // Load ESP settings
        settings.showDeadPlayersFUSER = bridge->shouldShowDeadPlayersFUSER();
        settings.showVehiclesFUSER = bridge->shouldShowVehiclesFUSER();
        settings.showGrounditemsFUSER = bridge->shouldShowGrounditemsFUSER();
        settings.showBoatsFUSER = bridge->shouldShowBoatsFUSER();
        settings.showDeadAnimalsFUSER = bridge->shouldShowDeadAnimalsFUSER();
        settings.showClothingFUSER = bridge->shouldShowClothingFUSER();
        settings.showWeaponsFUSER = bridge->shouldShowWeaponsFUSER();
        settings.showProxyMagazinesFUSER = bridge->shouldShowProxyMagazinesFUSER();
        settings.showBackpacksFUSER = bridge->shouldShowBackpacksFUSER();
        settings.showRareFUSER = bridge->shouldShowRareFUSER();
        settings.showFoodFUSER = bridge->shouldShowFoodFUSER();
        settings.showAmmoFUSER = bridge->shouldShowAmmoFUSER();
        settings.showZombiesFUSER = bridge->shouldShowZombiesFUSER();
        settings.showAnimalsFUSER = bridge->shouldShowAnimalsFUSER();
        settings.lootDistanceFUSER = bridge->shouldlootDistanceFUSER();
        settings.ZombieDistanceFUSER = bridge->shouldZombieDistanceFUSER();
        settings.showPlayerInfoesp = bridge->shouldShowPlayerInfoesp();
        settings.showPlayerNameFUSER = bridge->shouldShowPlayerNameFUSER();
        settings.showPlayerDistanceFUSER = bridge->shouldShowPlayerDistanceFUSER();
        settings.showPlayerHandFUSER = bridge->shouldShowPlayerHandFUSER();
        settings.showPlayerBoxFUSER = bridge->shouldShowPlayerBoxFUSER();
        settings.showOpticsFUSER = bridge->shouldShowOpticsFUSER();
        settings.showDebugFUSER = bridge->shouldShowDebugFUSER();
        settings.showBaseFUSER = bridge->shouldShowBaseFUSER();
        settings.showMeleeFUSER = bridge->shouldShowMeleeFUSER();
        settings.showExplosivesFUSER = bridge->shouldShowExplosivesFUSER();
        settings.LootDebugDistance = bridge->shouldLootDebugDistance();
        settings.showSavedLootItems = bridge->shouldShowSavedLootItems();
    }

} // namespace DMARender
