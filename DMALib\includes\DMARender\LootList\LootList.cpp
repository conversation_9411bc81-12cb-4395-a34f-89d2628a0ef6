#include "LootList.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cmath>

namespace DMARender {

    std::string SavedLootItem::toString() const {
        std::stringstream ss;
        auto timeSeconds = std::chrono::duration_cast<std::chrono::seconds>(timeFound.time_since_epoch()).count();

        // Format: itemName|itemType|cleanName|x|y|z|timeFound|distance|isRare|notes|entityId
        ss << itemName << "|" << itemType << "|" << cleanName << "|"
           << position.x << "|" << position.y << "|" << position.z << "|"
           << timeSeconds << "|" << distanceWhenSaved << "|" << (isRare ? 1 : 0) << "|"
           << notes << "|" << entityId;
        return ss.str();
    }

    SavedLootItem SavedLootItem::fromString(const std::string& str) {
        SavedLootItem item;
        std::stringstream ss(str);
        std::string token;
        std::vector<std::string> tokens;

        while (std::getline(ss, token, '|')) {
            tokens.push_back(token);
        }

        if (tokens.size() >= 11) {
            item.itemName = tokens[0];
            item.itemType = tokens[1];
            item.cleanName = tokens[2];
            item.position.x = std::stof(tokens[3]);
            item.position.y = std::stof(tokens[4]);
            item.position.z = std::stof(tokens[5]);

            auto timeSeconds = std::stoll(tokens[6]);
            item.timeFound = std::chrono::system_clock::from_time_t(timeSeconds);

            item.distanceWhenSaved = std::stof(tokens[7]);
            item.isRare = (tokens[8] == "1");
            item.notes = tokens[9];
            item.entityId = std::stoul(tokens[10]);
        }
        return item;
    }

    std::string SavedLootItem::getTimeString() const {
        auto time_t = std::chrono::system_clock::to_time_t(timeFound);
        std::tm tm_buf;
        localtime_s(&tm_buf, &time_t);
        std::stringstream ss;
        ss << std::put_time(&tm_buf, "%H:%M:%S");
        return ss.str();
    }

    LootListManager::LootListManager() : saveFilePath("saved_loot.txt"), isDirty(false) {
        loadFromFile();
    }

    LootListManager::~LootListManager() {
        if (isDirty) {
            saveToFile();
        }
    }

    void LootListManager::addItem(const SavedLootItem& item) {
        savedItems.push_back(item);
        isDirty = true;
    }

    void LootListManager::removeItem(size_t index) {
        if (index < savedItems.size()) {
            savedItems.erase(savedItems.begin() + index);
            isDirty = true;
        }
    }

    void LootListManager::clearAll() {
        savedItems.clear();
        isDirty = true;
    }

    void LootListManager::updateNotes(size_t index, const std::string& notes) {
        if (index < savedItems.size()) {
            savedItems[index].notes = notes;
            isDirty = true;
        }
    }

    std::vector<size_t> LootListManager::findItemsByName(const std::string& name) const {
        std::vector<size_t> indices;
        for (size_t i = 0; i < savedItems.size(); ++i) {
            if (savedItems[i].itemName.find(name) != std::string::npos ||
                savedItems[i].cleanName.find(name) != std::string::npos) {
                indices.push_back(i);
            }
        }
        return indices;
    }

    std::vector<size_t> LootListManager::findItemsByType(const std::string& type) const {
        std::vector<size_t> indices;
        for (size_t i = 0; i < savedItems.size(); ++i) {
            if (savedItems[i].itemType.find(type) != std::string::npos) {
                indices.push_back(i);
            }
        }
        return indices;
    }

    std::vector<size_t> LootListManager::findRareItems() const {
        std::vector<size_t> indices;
        for (size_t i = 0; i < savedItems.size(); ++i) {
            if (savedItems[i].isRare) {
                indices.push_back(i);
            }
        }
        return indices;
    }

    std::vector<size_t> LootListManager::findItemsNearPosition(const Vector3& pos, float maxDistance) const {
        std::vector<size_t> indices;
        for (size_t i = 0; i < savedItems.size(); ++i) {
            float distance = pos.Dist(savedItems[i].position);
            if (distance <= maxDistance) {
                indices.push_back(i);
            }
        }
        return indices;
    }

    bool LootListManager::saveToFile() {
        try {
            std::ofstream file(saveFilePath);
            if (!file.is_open()) {
                return false;
            }

            for (const auto& item : savedItems) {
                file << item.toString() << std::endl;
            }

            file.close();
            isDirty = false;
            return true;
        }
        catch (...) {
            return false;
        }
    }

    bool LootListManager::loadFromFile() {
        try {
            std::ifstream file(saveFilePath);
            if (!file.is_open()) {
                return false; // File doesn't exist yet, that's okay
            }

            savedItems.clear();
            std::string line;
            while (std::getline(file, line)) {
                if (!line.empty()) {
                    savedItems.push_back(SavedLootItem::fromString(line));
                }
            }

            file.close();
            isDirty = false;
            return true;
        }
        catch (...) {
            return false;
        }
    }

    void LootListManager::sortByDistance(const Vector3& currentPos) {
        std::sort(savedItems.begin(), savedItems.end(), 
            [&currentPos](const SavedLootItem& a, const SavedLootItem& b) {
                return currentPos.Dist(a.position) < currentPos.Dist(b.position);
            });
        isDirty = true;
    }

    void LootListManager::sortByTime() {
        std::sort(savedItems.begin(), savedItems.end(),
            [](const SavedLootItem& a, const SavedLootItem& b) {
                return a.timeFound > b.timeFound; // Newest first
            });
        isDirty = true;
    }

    void LootListManager::sortByName() {
        std::sort(savedItems.begin(), savedItems.end(),
            [](const SavedLootItem& a, const SavedLootItem& b) {
                return a.cleanName < b.cleanName;
            });
        isDirty = true;
    }

    int LootListManager::getItemCountByType(const std::string& type) const {
        return static_cast<int>(std::count_if(savedItems.begin(), savedItems.end(),
            [&type](const SavedLootItem& item) {
                return item.itemType.find(type) != std::string::npos;
            }));
    }

    float LootListManager::getAverageDistance() const {
        if (savedItems.empty()) return 0.0f;
        
        float total = 0.0f;
        for (const auto& item : savedItems) {
            total += item.distanceWhenSaved;
        }
        return total / savedItems.size();
    }

} // namespace DMARender
