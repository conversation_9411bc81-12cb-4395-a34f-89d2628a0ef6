#pragma once
#include <string>
#include <fstream>
#include <iostream>

namespace DMARender {

    struct AppSettings {
        // Radar Settings
        bool showDeadPlayers = true;
        bool showVehicles = true;
        bool showGrounditems = false;
        bool showBoats = true;
        bool showDeadAnimals = false;
        bool showClothing = false;
        bool showWeapons = true;
        bool showProxyMagazines = true;
        bool showBackpacks = true;
        bool showRare = true;
        bool showFood = true;
        bool showAmmo = true;
        bool showPlayerList = true;
        bool showServerPlayerList = false;
        bool showZombies = false;
        bool showAnimals = false;
        std::string playername = "Survivor";
        int BlipSize = 7;
        int BlipSize2 = 4;
        int Aimlinelength = 80;
        bool showOptics = true;
        bool showBase = true;
        bool showMelee = true;
        int LootDistanceDeadzone = 0;
        bool showExplosives = true;
        int ZombiesBlipSize = 4;
        int AnimalsBlipSize = 5;
        int FontRADAR = 16;
        int FontRADAR2 = 14;
        bool showPlayerNameRadar = true;
        bool showPlayerDistanceRadar = true;
        bool showPlayerHandRadar = true;

        // ESP/FUSER Settings
        bool showDeadPlayersFUSER = true;
        bool showVehiclesFUSER = true;
        bool showGrounditemsFUSER = true;
        bool showBoatsFUSER = true;
        bool showDeadAnimalsFUSER = false;
        bool showClothingFUSER = false;
        bool showWeaponsFUSER = true;
        bool showProxyMagazinesFUSER = true;
        bool showBackpacksFUSER = true;
        bool showRareFUSER = true;
        bool showFoodFUSER = true;
        bool showAmmoFUSER = true;
        bool showZombiesFUSER = false;
        bool showAnimalsFUSER = false;
        int lootDistanceFUSER = 70;
        int ZombieDistanceFUSER = 100;
        bool showPlayerInfoesp = true;
        bool showPlayerNameFUSER = true;
        bool showPlayerDistanceFUSER = true;
        bool showPlayerHandFUSER = true;
        bool showPlayerBoxFUSER = true;
        bool showOpticsFUSER = true;
        bool showDebugFUSER = false;
        bool showBaseFUSER = true;
        bool showMeleeFUSER = true;
        bool showExplosivesFUSER = true;
        int LootDebugDistance = 70;
        bool showSavedLootItems = true;

        // UI Settings
        bool followPlayerEnabled = false;
    };

    class SettingsManager {
    private:
        std::string settingsFilePath;
        AppSettings settings;

    public:
        SettingsManager(const std::string& filePath = "app_settings.ini");
        
        // Load/Save functions
        bool loadSettings();
        bool saveSettings();
        
        // Getters
        const AppSettings& getSettings() const { return settings; }
        AppSettings& getSettings() { return settings; }
        
        // Apply settings to RenderBridge
        void applyToRenderBridge(class RenderBridge* bridge);
        void loadFromRenderBridge(class RenderBridge* bridge);

    private:
        void parseSettingValue(const std::string& key, const std::string& value);
    };

} // namespace DMARender
