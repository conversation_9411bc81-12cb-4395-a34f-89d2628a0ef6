﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="includes\DMAMemoryManagement\includes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMAMemoryManagement\VmmManager\VmmManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMAMemoryManagement\Utils\Utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMAMemoryManagement\StaticManager\StaticManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMAMemoryManagement\MemProcFS\vmmdll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMAMemoryManagement\MemProcFS\leechcore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMAMemoryManagement\MemoryObject\PointerChain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMAMemoryManagement\MemoryObject\MemoryObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_truetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_rectpack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_textedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\backends\imgui_impl_win32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\misc\cpp\imgui_stdlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\includes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\RenderWindow\RenderWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\backends\imgui_impl_dx11.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\IOverlay\IOverlay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\Vectors\Vector3.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\Vectors\Vector2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\RenderWindow\RenderBridge\RenderBridge.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\ImageAllocator\ImageAllocator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\ImageAllocator\stb_image.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\IGameMap\IGameMap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\RenderWindow\RenderBridge\MapManager\MapManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\IRadar\IRadar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="includes\DMARender\RenderWindow\MapTransform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="DMALib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMAMemoryManagement\VmmManager\VmmManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMAMemoryManagement\Utils\Utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMAMemoryManagement\StaticManager\StaticManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMAMemoryManagement\MemoryObject\MemoryObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_demo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_draw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_tables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_widgets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\backends\imgui_impl_win32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\misc\cpp\imgui_stdlib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMARender\RenderWindow\RenderWindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\backends\imgui_impl_dx11.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMARender\IOverlay\IOverlay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMARender\Vectors\Vector3.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMARender\RenderWindow\RenderBridge\RenderBridge.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMARender\ImageAllocator\ImageAllocator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMARender\IGameMap\IGameMap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMARender\RenderWindow\RenderBridge\MapManager\MapManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="includes\DMARender\IRadar\IRadar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Library Include="libs\leechcore.lib" />
    <Library Include="libs\vmm.lib" />
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="libs\FTD3XX.dll" />
    <CopyFileToFolders Include="libs\leechcore.dll" />
    <CopyFileToFolders Include="libs\vmm.dll" />
    <CopyFileToFolders Include="libs\dbghelp.dll" />
    <CopyFileToFolders Include="libs\info.db" />
    <CopyFileToFolders Include="libs\symsrv.dll" />
    <CopyFileToFolders Include="libs\symsrv.yes" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="DMALib.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Image Include="..\dma-dayz-cpp\53ac2d01e41ff949c11c916309ae17b7.ico">
      <Filter>Resource Files</Filter>
    </Image>
  </ItemGroup>
</Project>